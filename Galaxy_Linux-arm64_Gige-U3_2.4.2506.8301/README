System Requirements
====================

g++ version >= 5.0.x
glibc version >= 2.17

USB3.0 Vision Camera:
   For best performance and stability we highly recommend a kernel version >= 3.5.0.23
   
GigE Vision Camera:
   For best performance and stability we highly recommend a kernel version >= 2.6.13


Test
====================

The SDK package runs well in Ubuntu, eg 12.04, 14.04, 16.04, 18.04, 20.04.
Other linux distribution systems have not been tested, if used, there may be some usage problems.


Installation
====================

1. Installation command: "./Galaxy_camera.run"

2. When you do not see any error message in the installation and the end shown as below, your installation is compelete.
-----------------------------------------------------------------
All configurations will take effect after the system is rebooted.
If you don't want to reboot the system for a while,
you will need to unplug and replug the camera before using SDK.
-----------------------------------------------------------------
3.If you would like to run GxViewer or GxGigeIPConfig, You have to make sure that Qt4 has been installed in your system. 
    Install Qt4 command in some system: sudo apt-get install qt4-default  

Notes:
    1. After your installation, You need to replug Galaxy USB3 device before using SDK.
    2. Don't install the SDK in a folder which path has chinese characters, or SDK may not work. 



Troubleshooting
====================
* In installation, if show the following message:

  "service: Command not found" 

  or

  "udev: unrecognized service"

  It means your system do not have "udev" service but still have "udev" directory. We can't help you get device permissions automatically. In this case, you need to use "sudo" to run your appliction when using U3V cameras.    
  
* Problem: The GxGigeIPConfig can't see my camera. I'm not able to reconfigure the camera to make it visible again.

  Solution: First make sure you don't have a firewall enabled on your network interface the camera is connected to. If you still can't see the camera, reverse path filtering in the kernel may prevent the GxGigeIPConfig to detect the camera. On some Linux  distributions reverse path filtering may prevent the discovery of GigE Vision cameras. This can happen if the camera's IP address is not within the same subnet as the network adapter the camera is attached to. Normally the GxGigeIPConfig can handle this by using broadcasts to discover the camera on any subnet. Reverse-path filtering may prevent the GxGigeIPConfig to receive the answer from the broadcast which in turn prevents the GxGigeIPConfig from detecting the camera. To check whether filtering is turned on, run the following command:
    sysctl -a 2>/dev/null | grep '\.rp_filter'

  in the output look for the following lines:
   net.ipv4.conf.all.rp_filter=1
   net.ipv4.conf.eth1.rp_filter=1

  where "eth1" is the network adapter the camera is connected to.
  The "net.ipv4.conf.all.rp_filter" is a global switch which must be turned off.
  The "net.ipv4.conf.eth1.rp_filter" tells whether filtering for the specified network adapter is activated. To disable filtering, you must first turn off filtering for "all" and the specific network interface (in this sample "eth1"). Use the following commands to change the filtering behavior at runtime:
    sudo sysctl net.ipv4.conf.all.rp_filter=0
    sudo sysctl net.ipv4.conf.eth1.rp_filter=0

  Restart the IP Configurator and check whether the camera(s) are detected. Reconfigure the camera(s) and use "Write Configuration" to make your changes persistent. You can re-enable filtering by executing the same commands but set a value of 1. If you want to turn off filtering permanently, you can edit the same values in /etc/sysctl.conf.

  From Linux kernel version 2.6.32 onwards, the rp_filter settings allow strict and loose filtering. To accept asymmetrically routed packets, modify /etc/sysctl.conf:
    net.ipv4.conf.default.rp_filter = 2
    net.ipv4.conf.all.rp_filter = 2
     
* Problem: When I use GalaxyView in Ubuntu 18.04.* with root permissions, it only show a white UI.
  Solution: You can run GalaxyView by this command:
  sudo QT_X11_NO_MITSHM=1 ./GalaxyView.
  or
  add 'QT_X11_NO_MITSHM=1' to system env.
          
Performance
====================

1.When using USB3.0 Vision Camera,If you need grab image from 4 or more U3V cameras, or you need increasing the package(URB) size or count, you will likely run out of kernel space and see corresponding error messages on the console. Because of the default value of USB Kernel Space set by the kernel is 16 MB. To set the value (in this example to 1000 MB) you can
  execute as root:
      echo 1000 > /sys/module/usbcore/parameters/usbfs_memory_mb
  or execute ./SetUSBStack.sh as root.
  This would assign a maximum of 1000 MB to the USB stack.

2.Performance Optimization
To increase performance and to minimize CPU usage when grabbing images, the following settings should be considered:

USB3.0 vision Camera
 Increasing Packet Size
 For faster USB transfers you should increase the packet size. You can do this by changing "StreamTransferSize" value by setting the corresponding value via the API.
 
GigE vision Camera
* Enable Jumbo Frames.
  Many GigE network adapters support so-called jumbo frames, i.e., network packets larger than the usual 1500 bytes. To enable jumbo frames, the maximum transfer unit (MTU) size of the PC's network adapter must be set to a high value. We recommend using a value of 8192.

* Increasing Packet Size.
  When jumbo frames are enabled, the camera's packet size must be increased to benefit from the larger packets. 
  
* Disabled the Firewall.
  The firewall must be disabled for network adapters to which cameras are connected. Otherwise, device discovery and receiving streaming data may not work.

