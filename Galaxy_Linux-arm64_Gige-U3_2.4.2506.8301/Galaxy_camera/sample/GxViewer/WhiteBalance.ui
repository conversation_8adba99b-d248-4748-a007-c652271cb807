<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CWhiteBalance</class>
 <widget class="QDialog" name="CWhiteBalance">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>351</width>
    <height>292</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>WhiteBalance</string>
  </property>
  <widget class="QGroupBox" name="Balance_White">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>12</y>
     <width>331</width>
     <height>241</height>
    </rect>
   </property>
   <property name="title">
    <string>Balance White</string>
   </property>
   <widget class="QComboBox" name="BalanceRatioSelector">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>24</y>
      <width>131</width>
      <height>24</height>
     </rect>
    </property>
    <property name="editable">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="BalanceRatioSelectorLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>30</y>
      <width>141</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>BalanceRatioSelector</string>
    </property>
   </widget>
   <widget class="QLabel" name="BalanceRatioLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>60</y>
      <width>121</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>BalanceRatio</string>
    </property>
   </widget>
   <widget class="QLabel" name="AWBROIHeightLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>150</y>
      <width>151</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>AWBROIHeight</string>
    </property>
   </widget>
   <widget class="QSlider" name="AWBROIHeightSlider">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>149</y>
      <width>60</width>
      <height>20</height>
     </rect>
    </property>
    <property name="inputMethodHints">
     <set>Qt::ImhNone</set>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
    <property name="invertedControls">
     <bool>false</bool>
    </property>
    <property name="tickPosition">
     <enum>QSlider::NoTicks</enum>
    </property>
   </widget>
   <widget class="QDoubleSpinBox" name="BalanceRatioSpin">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>54</y>
      <width>131</width>
      <height>24</height>
     </rect>
    </property>
   </widget>
   <widget class="QSpinBox" name="AWBROIHeightSpin">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>144</y>
      <width>61</width>
      <height>24</height>
     </rect>
    </property>
   </widget>
   <widget class="QLabel" name="AWBROIWidthLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>120</y>
      <width>151</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>AWBROIWidth</string>
    </property>
   </widget>
   <widget class="QSpinBox" name="AWBROIWidthSpin">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>114</y>
      <width>61</width>
      <height>24</height>
     </rect>
    </property>
   </widget>
   <widget class="QSlider" name="AWBROIWidthSlider">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>119</y>
      <width>60</width>
      <height>20</height>
     </rect>
    </property>
    <property name="inputMethodHints">
     <set>Qt::ImhNone</set>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
    <property name="invertedControls">
     <bool>false</bool>
    </property>
    <property name="tickPosition">
     <enum>QSlider::NoTicks</enum>
    </property>
   </widget>
   <widget class="QSpinBox" name="AWBROIOffsetYSpin">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>204</y>
      <width>61</width>
      <height>24</height>
     </rect>
    </property>
   </widget>
   <widget class="QSlider" name="AWBROIOffsetYSlider">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>209</y>
      <width>60</width>
      <height>20</height>
     </rect>
    </property>
    <property name="inputMethodHints">
     <set>Qt::ImhNone</set>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
    <property name="invertedControls">
     <bool>false</bool>
    </property>
    <property name="tickPosition">
     <enum>QSlider::NoTicks</enum>
    </property>
   </widget>
   <widget class="QSlider" name="AWBROIOffsetXSlider">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>179</y>
      <width>60</width>
      <height>20</height>
     </rect>
    </property>
    <property name="inputMethodHints">
     <set>Qt::ImhNone</set>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
    <property name="invertedControls">
     <bool>false</bool>
    </property>
    <property name="tickPosition">
     <enum>QSlider::NoTicks</enum>
    </property>
   </widget>
   <widget class="QSpinBox" name="AWBROIOffsetXSpin">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>174</y>
      <width>61</width>
      <height>24</height>
     </rect>
    </property>
   </widget>
   <widget class="QLabel" name="AWBROIOffsetXLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>180</y>
      <width>151</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>AWBROIOffsetX</string>
    </property>
   </widget>
   <widget class="QLabel" name="AWBROIOffsetYLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>210</y>
      <width>151</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>AWBROIOffsetY</string>
    </property>
   </widget>
   <widget class="QComboBox" name="WhiteBalanceAuto">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>84</y>
      <width>131</width>
      <height>24</height>
     </rect>
    </property>
    <property name="editable">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="WhiteBalanceRatioLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>90</y>
      <width>141</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>WhiteBalanceAuto</string>
    </property>
   </widget>
  </widget>
  <widget class="QPushButton" name="WhiteBalance_Close">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>260</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
   <property name="focusPolicy">
    <enum>Qt::StrongFocus</enum>
   </property>
   <property name="text">
    <string>Close</string>
   </property>
  </widget>
 </widget>
 <tabstops>
  <tabstop>BalanceRatioSelector</tabstop>
  <tabstop>BalanceRatioSpin</tabstop>
  <tabstop>WhiteBalanceAuto</tabstop>
  <tabstop>AWBROIWidthSlider</tabstop>
  <tabstop>AWBROIWidthSpin</tabstop>
  <tabstop>AWBROIHeightSlider</tabstop>
  <tabstop>AWBROIHeightSpin</tabstop>
  <tabstop>AWBROIOffsetXSlider</tabstop>
  <tabstop>AWBROIOffsetXSpin</tabstop>
  <tabstop>AWBROIOffsetYSlider</tabstop>
  <tabstop>AWBROIOffsetYSpin</tabstop>
  <tabstop>WhiteBalance_Close</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
