<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CImageImprovement</class>
 <widget class="QDialog" name="CImageImprovement">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>351</width>
    <height>172</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Image Improvement</string>
  </property>
  <widget class="QGroupBox" name="ImageImprovement">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>331</width>
     <height>121</height>
    </rect>
   </property>
   <property name="title">
    <string>Image Improvement</string>
   </property>
   <widget class="QSpinBox" name="ContrastSpin">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>80</y>
      <width>61</width>
      <height>24</height>
     </rect>
    </property>
   </widget>
   <widget class="QSlider" name="ContrastSlider">
    <property name="geometry">
     <rect>
      <x>159</x>
      <y>85</y>
      <width>81</width>
      <height>20</height>
     </rect>
    </property>
    <property name="inputMethodHints">
     <set>Qt::ImhNone</set>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
    <property name="invertedControls">
     <bool>false</bool>
    </property>
    <property name="tickPosition">
     <enum>QSlider::NoTicks</enum>
    </property>
   </widget>
   <widget class="QDoubleSpinBox" name="GammaSpin">
    <property name="geometry">
     <rect>
      <x>160</x>
      <y>50</y>
      <width>151</width>
      <height>24</height>
     </rect>
    </property>
    <property name="correctionMode">
     <enum>QAbstractSpinBox::CorrectToPreviousValue</enum>
    </property>
   </widget>
   <widget class="QCheckBox" name="GammaCheckBox">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>53</y>
      <width>86</width>
      <height>20</height>
     </rect>
    </property>
    <property name="text">
     <string>Gamma</string>
    </property>
   </widget>
   <widget class="QCheckBox" name="ContrastCheckBox">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>83</y>
      <width>141</width>
      <height>20</height>
     </rect>
    </property>
    <property name="text">
     <string>Contrast</string>
    </property>
   </widget>
   <widget class="QCheckBox" name="ColorCorrect">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>23</y>
      <width>111</width>
      <height>20</height>
     </rect>
    </property>
    <property name="text">
     <string>ColorCorrect</string>
    </property>
   </widget>
  </widget>
  <widget class="QPushButton" name="ImageImprovement_Close">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>140</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
   <property name="text">
    <string>Close</string>
   </property>
  </widget>
 </widget>
 <tabstops>
  <tabstop>ColorCorrect</tabstop>
  <tabstop>GammaCheckBox</tabstop>
  <tabstop>GammaSpin</tabstop>
  <tabstop>ContrastCheckBox</tabstop>
  <tabstop>ContrastSlider</tabstop>
  <tabstop>ContrastSpin</tabstop>
  <tabstop>ImageImprovement_Close</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
