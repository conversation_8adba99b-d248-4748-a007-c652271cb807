<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CRoi</class>
 <widget class="QDialog" name="CRoi">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>351</width>
    <height>214</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ROISettings</string>
  </property>
  <widget class="QGroupBox" name="ROISettings">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>331</width>
     <height>161</height>
    </rect>
   </property>
   <property name="title">
    <string>ROI Settings</string>
   </property>
   <widget class="QSpinBox" name="OffsetXSpin">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>88</y>
      <width>61</width>
      <height>24</height>
     </rect>
    </property>
   </widget>
   <widget class="QSlider" name="HeightSlider">
    <property name="geometry">
     <rect>
      <x>109</x>
      <y>58</y>
      <width>131</width>
      <height>20</height>
     </rect>
    </property>
    <property name="inputMethodHints">
     <set>Qt::ImhNone</set>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
    <property name="invertedControls">
     <bool>false</bool>
    </property>
    <property name="tickPosition">
     <enum>QSlider::NoTicks</enum>
    </property>
   </widget>
   <widget class="QLabel" name="OffsetXLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>94</y>
      <width>91</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>OffsetX</string>
    </property>
   </widget>
   <widget class="QLabel" name="HeightLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>59</y>
      <width>91</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>Height</string>
    </property>
   </widget>
   <widget class="QSpinBox" name="HeightSpin">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>53</y>
      <width>61</width>
      <height>24</height>
     </rect>
    </property>
   </widget>
   <widget class="QSpinBox" name="OffsetYSpin">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>123</y>
      <width>61</width>
      <height>24</height>
     </rect>
    </property>
   </widget>
   <widget class="QLabel" name="WidthLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>24</y>
      <width>91</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>Width</string>
    </property>
   </widget>
   <widget class="QSlider" name="WidthSlider">
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>23</y>
      <width>131</width>
      <height>20</height>
     </rect>
    </property>
    <property name="inputMethodHints">
     <set>Qt::ImhNone</set>
    </property>
    <property name="singleStep">
     <number>4</number>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
    <property name="invertedControls">
     <bool>false</bool>
    </property>
    <property name="tickPosition">
     <enum>QSlider::NoTicks</enum>
    </property>
   </widget>
   <widget class="QSpinBox" name="WidthSpin">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>18</y>
      <width>61</width>
      <height>24</height>
     </rect>
    </property>
   </widget>
   <widget class="QLabel" name="OffsetYLabel">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>129</y>
      <width>91</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>OffsetY</string>
    </property>
   </widget>
   <widget class="QSlider" name="OffsetYSlider">
    <property name="geometry">
     <rect>
      <x>109</x>
      <y>128</y>
      <width>131</width>
      <height>20</height>
     </rect>
    </property>
    <property name="inputMethodHints">
     <set>Qt::ImhNone</set>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
    <property name="invertedControls">
     <bool>false</bool>
    </property>
    <property name="tickPosition">
     <enum>QSlider::NoTicks</enum>
    </property>
   </widget>
   <widget class="QSlider" name="OffsetXSlider">
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>93</y>
      <width>131</width>
      <height>20</height>
     </rect>
    </property>
    <property name="inputMethodHints">
     <set>Qt::ImhNone</set>
    </property>
    <property name="singleStep">
     <number>4</number>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
    <property name="invertedAppearance">
     <bool>false</bool>
    </property>
    <property name="invertedControls">
     <bool>false</bool>
    </property>
    <property name="tickPosition">
     <enum>QSlider::NoTicks</enum>
    </property>
   </widget>
  </widget>
  <widget class="QPushButton" name="ROISettingClose">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>180</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
   <property name="text">
    <string>Close</string>
   </property>
  </widget>
 </widget>
 <tabstops>
  <tabstop>WidthSlider</tabstop>
  <tabstop>WidthSpin</tabstop>
  <tabstop>HeightSlider</tabstop>
  <tabstop>HeightSpin</tabstop>
  <tabstop>OffsetXSlider</tabstop>
  <tabstop>OffsetXSpin</tabstop>
  <tabstop>OffsetYSlider</tabstop>
  <tabstop>OffsetYSpin</tabstop>
  <tabstop>ROISettingClose</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
