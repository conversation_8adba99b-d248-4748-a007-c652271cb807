log4cplus.rootLogger=ALL, SHARED_FILE

# U3VTL 模块配置
log4cplus.logger.U3VTL=ALL, SHARED_FILE
log4cplus.additivity.U3VTL=false

# GVTL 模块配置
log4cplus.logger.GVTL=ALL, SHARED_FILE
log4cplus.additivity.GVTL=false

# GXIAPI 模块配置
log4cplus.logger.GXIAPI=ALL, SHARED_FILE
log4cplus.additivity.GXIAPI=false

# 共享的日志文件配置
log4cplus.appender.SHARED_FILE=log4cplus::RollingFileAppender
log4cplus.appender.SHARED_FILE.File=/var/log/Galaxy/SDK.log
log4cplus.appender.SHARED_FILE.CreateDirs=true
log4cplus.appender.SHARED_FILE.MaxFileSize=100MB
log4cplus.appender.SHARED_FILE.MaxBackupIndex=3
log4cplus.appender.SHARED_FILE.layout=log4cplus::PatternLayout
log4cplus.appender.SHARED_FILE.layout.ConversionPattern=%D{%Y-%m-%d %H:%M:%S:%q};%p;%t;%c;[%l];%m%n

# U3VTL 模块的过滤器配置
log4cplus.appender.SHARED_FILE.filters.1=log4cplus::spi::LogLevelMatchFilter
log4cplus.appender.SHARED_FILE.filters.1.LogLevelToMatch=FATAL
log4cplus.appender.SHARED_FILE.filters.1.AcceptOnMatch=1

log4cplus.appender.SHARED_FILE.filters.2=log4cplus::spi::LogLevelMatchFilter
log4cplus.appender.SHARED_FILE.filters.2.LogLevelToMatch=ERROR
log4cplus.appender.SHARED_FILE.filters.2.AcceptOnMatch=1

log4cplus.appender.SHARED_FILE.filters.3=log4cplus::spi::LogLevelMatchFilter
log4cplus.appender.SHARED_FILE.filters.3.LogLevelToMatch=WARN
log4cplus.appender.SHARED_FILE.filters.3.AcceptOnMatch=1

log4cplus.appender.SHARED_FILE.filters.4=log4cplus::spi::LogLevelMatchFilter
log4cplus.appender.SHARED_FILE.filters.4.LogLevelToMatch=INFO
log4cplus.appender.SHARED_FILE.filters.4.AcceptOnMatch=1

log4cplus.appender.SHARED_FILE.filters.5=log4cplus::spi::LogLevelMatchFilter
log4cplus.appender.SHARED_FILE.filters.5.LogLevelToMatch=DEBUG
log4cplus.appender.SHARED_FILE.filters.5.AcceptOnMatch=0

log4cplus.appender.SHARED_FILE.filters.6=log4cplus::spi::LogLevelMatchFilter
log4cplus.appender.SHARED_FILE.filters.6.LogLevelToMatch=TRACE
log4cplus.appender.SHARED_FILE.filters.6.AcceptOnMatch=0

log4cplus.appender.SHARED_FILE.filters.7=log4cplus::spi::DenyAllFilter

