import gxipy as gx
import os
import time
import numpy as np

# --- 配置 ---
output_folder = 'input'
if not os.path.exists(output_folder):
    os.makedirs(output_folder)

# --- 主采集函数 ---
def capture_images():
    cam = None
    try:
        # --- 1. 初始化与打开设备 ---
        print("正在初始化相机设备...")
        device_manager = gx.DeviceManager()
        dev_num, dev_info_list = device_manager.update_device_list()
        if dev_num == 0:
            print("❌ 错误: 未找到任何设备。")
            return

        cam = device_manager.open_device_by_index(1)
        print("✅ 相机已成功打开。")

        # --- 2. 获取元数据 ---
        width = cam.Width.get()
        height = cam.Height.get()
        
        pixel_format_enum = cam.PixelFormat.get()
        
        try:
            pixel_format_str = cam.PixelFormat.get_symbolic()
        except AttributeError:
            print(f"警告: get_symbolic() 方法不存在，将使用备用方法进行格式名称映射。")
            # 备用方案：直接从枚举值中提取字符串
            if isinstance(pixel_format_enum, tuple) and len(pixel_format_enum) > 1:
                pixel_format_str = pixel_format_enum[1]
            else:
                # 如果还不行，就只能用一个更通用的查找表
                pixel_format_map = { 17301505: 'Mono8', 17825797: 'Mono12', 17825798: 'Mono16' }
                pixel_format_str = pixel_format_map.get(pixel_format_enum, f"Unknown_{pixel_format_enum}")

        print("\n" + "="*50)
        print("          相机当前配置")
        print("="*50)
        print(f"  图像尺寸: {width} x {height}")
        print(f"  像素格式: {pixel_format_str}")
        print("="*50 + "\n")

        # 根据像素格式字符串，确定要保存的正确NumPy数据类型
        if 'Mono8' in pixel_format_str:
            target_dtype = np.uint8
        elif 'Mono12' in pixel_format_str or 'Mono16' in pixel_format_str:
            target_dtype = np.uint16
        else:
            print(f"⚠️ 警告: 当前像素格式 '{pixel_format_str}' 不是推荐的 Mono8 或 Mono12/16。")
            target_dtype = np.uint8 # 默认使用8位

        # --- 3. 开始采集循环 ---
        cam.stream_on()
        print(">>> 开始采集图像，按 Ctrl+C 停止...")

        while True:
            raw_image = cam.data_stream[0].get_image(timeout=1000)
            if raw_image is None:
                print("获取图片超时，继续...")
                continue

            # --- 核心修改：直接与成功状态码 0 比较 ---
            if raw_image.get_status() != 0: # 0 通常代表 GX_STATUS_SUCCESS
                print(f"图像帧状态不完整 (状态码: {raw_image.get_status()})，跳过。")
                continue

            numpy_image = raw_image.get_numpy_array()
            if numpy_image is None:
                continue

            if numpy_image.dtype != target_dtype:
                numpy_image = numpy_image.astype(target_dtype)

            timestamp = time.strftime("%Y%m%d%H%M%S")
            filename = f"Pic_{timestamp}_W{width}_H{height}_F{pixel_format_str}.raw"
            full_path = os.path.join(output_folder, filename)

            numpy_image.tofile(full_path)
            print(f"✅ 已保存: {filename} (数据类型: {numpy_image.dtype})")

            time.sleep(30)

    except KeyboardInterrupt:
        print("\n>>> 收到停止指令，正在关闭相机...")
    except Exception as e:
        print(f"❌ 发生未知错误: {str(e)}")

    finally:
        if cam is not None:
            try:
                cam.stream_off()
                print("相机数据流已停止。")
                cam.close_device()
                print("相机已关闭。")
            except Exception as e:
                print(f"关闭相机时出错: {e}")

if __name__ == '__main__':
    capture_images()