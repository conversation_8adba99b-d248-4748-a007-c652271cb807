#!/bin/bash

# Galaxy Camera Mono12 Capture Script
# 使用正确的Python环境运行图像采集程序

echo "🚀 启动Galaxy相机Mono12图像采集程序..."
echo "📍 工作目录: $(pwd)"
echo "🐍 Python版本: $(/home/<USER>/miniconda3/bin/python3 --version)"
echo ""

# 检查相机连接
echo "🔍 检查相机连接状态..."
if lsusb | grep -i "galaxy\|daheng" > /dev/null; then
    echo "✅ 检测到Galaxy相机设备"
elif lsusb | grep -i "usb.*vision\|gige" > /dev/null; then
    echo "✅ 检测到可能的相机设备"
else
    echo "⚠️  未检测到明显的相机设备，但程序仍会尝试连接"
fi

echo ""
echo "📸 开始运行图像采集程序..."
echo "💡 提示: 按 Ctrl+C 停止采集"
echo "📁 图片将保存到: ./input/ 目录"
echo ""

# 使用正确的Python路径运行程序
/home/<USER>/miniconda3/bin/python3 GxSimpleCapture.py

echo ""
echo "🏁 程序已结束"
