import gxipy as gx
import os
import time
import numpy as np
import configparser

# 尝试导入PIL，如果失败则禁用PNG保存功能
try:
    from PIL import Image
    PIL_AVAILABLE = True
    print("✅ PIL/Pillow 可用，PNG保存功能已启用")
except ImportError:
    PIL_AVAILABLE = False
    print("⚠️ 警告: PIL/Pillow 不可用，PNG保存功能已禁用")
    print("   如需PNG保存功能，请安装: pip install Pillow")

# --- 1. 读取配置 ---
def read_config():
    """读取配置文件并验证参数"""
    config = configparser.ConfigParser()

    # 检查配置文件是否存在
    if not os.path.exists('config.ini'):
        print("❌ 错误: 配置文件 'config.ini' 不存在！")
        print("请确保配置文件存在于当前目录中。")
        return None

    try:
        config.read('config.ini')
        print("✅ 配置文件读取成功")
    except Exception as e:
        print(f"❌ 错误: 读取配置文件失败: {e}")
        return None

    return config

config = read_config()
if config is None:
    exit(1)

# [General] settings
try:
    output_folder = config.get('General', 'OutputFolder', fallback='input')
    output_folder_png = config.get('General', 'OutputFolderPng', fallback=f"{output_folder}_png")
    capture_interval = config.getint('General', 'CaptureInterval', fallback=30)

    # 验证采集间隔
    if capture_interval <= 0:
        print("⚠️ 警告: CaptureInterval 必须大于0，使用默认值30秒")
        capture_interval = 30

except Exception as e:
    print(f"❌ 错误: 读取General配置失败: {e}")
    exit(1)

# [Camera] settings
try:
    exposure_time = config.getint('Camera', 'ExposureTime', fallback=-1)

    # 验证曝光时间
    if exposure_time != -1 and exposure_time <= 0:
        print("⚠️ 警告: ExposureTime 必须大于0或为-1(自动曝光)，使用自动曝光")
        exposure_time = -1

except Exception as e:
    print(f"❌ 错误: 读取Camera配置失败: {e}")
    exit(1)

# --- 2. 智能计算ROI参数 ---
# 初始化为默认全尺寸
offset_x, offset_y, width, height = -1, -1, -1, -1

try:
    roi_enabled = config.getboolean('ROI', 'Enabled', fallback=False)
    if roi_enabled:
        print("✅ ROI 功能已启用，正在计算硬件参数...")

        # 添加fallback值以防配置文件中缺少这些参数
        max_w = config.getint('ROI', 'MaxWidth', fallback=5120)
        max_h = config.getint('ROI', 'MaxHeight', fallback=5120)

        # 验证最大尺寸
        if max_w <= 0 or max_h <= 0:
            print("❌ 错误: MaxWidth 和 MaxHeight 必须大于0")
            exit(1)

        start_x = config.getint('ROI', 'StartX', fallback=-1)
        start_y = config.getint('ROI', 'StartY', fallback=-1)
        end_x = config.getint('ROI', 'EndX', fallback=-1)
        end_y = config.getint('ROI', 'EndY', fallback=-1)

        # 验证ROI坐标范围
        if start_x >= max_w or start_y >= max_h:
            print(f"❌ 错误: StartX({start_x}) 或 StartY({start_y}) 超出最大尺寸({max_w}x{max_h})")
            exit(1)

        if end_x != -1 and end_x > max_w:
            print(f"❌ 错误: EndX({end_x}) 超出最大宽度({max_w})")
            exit(1)

        if end_y != -1 and end_y > max_h:
            print(f"❌ 错误: EndY({end_y}) 超出最大高度({max_h})")
            exit(1)

        # 计算 OffsetX 和 Width
        offset_x = start_x if start_x != -1 else 0
        if end_x != -1:
            width = end_x - offset_x
            if width <= 0:
                print(f"❌ 错误: EndX({end_x}) 必须大于 StartX({start_x})")
                exit(1)
        else:
            width = max_w - offset_x

        # 计算 OffsetY 和 Height
        offset_y = start_y if start_y != -1 else 0
        if end_y != -1:
            height = end_y - offset_y
            if height <= 0:
                print(f"❌ 错误: EndY({end_y}) 必须大于 StartY({start_y})")
                exit(1)
        else:
            height = max_h - offset_y

        print(f"   用户设置 -> Start:({start_x},{start_y}), End:({end_x},{end_y})")
        print(f"   计算结果 -> Offset:({offset_x},{offset_y}), Size:({width},{height})")

        # 提醒用户检查步长
        # 大恒相机通常要求 Offset 和 Width/Height 是4或2的倍数
        if any(val % 4 != 0 for val in [offset_x, width, offset_y, height] if val != -1):
             print("⚠️ 警告: 计算出的ROI参数可能不符合相机硬件的步长要求(通常是2或4的倍数)。相机可能会自动校正这些值。")

    else:
        print("ℹ️ ROI 功能未启用，将使用相机最大分辨率。")

except Exception as e:
    print(f"❌ 错误: 读取ROI配置失败: {e}")
    exit(1)

# --- 确保输出文件夹存在 ---
if not os.path.exists(output_folder):
    os.makedirs(output_folder)
if not os.path.exists(output_folder_png):
    os.makedirs(output_folder_png)

# --- 主采集函数 ---
def capture_images():
    cam = None
    try:
        # --- 初始化与打开设备 ---
        print("\n正在初始化相机设备...")
        device_manager = gx.DeviceManager()
        dev_num, dev_info_list = device_manager.update_device_list()

        if dev_num == 0:
            print("❌ 错误: 未找到任何设备。")
            print("请检查:")
            print("  1. 相机是否正确连接")
            print("  2. 相机驱动是否已安装")
            print("  3. 相机是否被其他程序占用")
            return

        print(f"✅ 找到 {dev_num} 个设备")

        # 使用第一个设备 (索引0)
        try:
            cam = device_manager.open_device_by_index(1)  # 注意：这里使用索引1，如果只有一个设备应该改为0
            print("✅ 相机已成功打开。")
        except Exception as e:
            print(f"❌ 错误: 无法打开设备索引1: {e}")
            if dev_num >= 1:
                print("尝试使用设备索引0...")
                try:
                    cam = device_manager.open_device_by_index(0)
                    print("✅ 相机已成功打开 (使用索引0)。")
                except Exception as e2:
                    print(f"❌ 错误: 无法打开设备索引0: {e2}")
                    return
            else:
                return

        # --- 应用相机参数配置 ---
        print("\n--- 正在应用相机配置 ---")
        
        # 1. 设置曝光
        if exposure_time != -1:
            cam.ExposureTime.set(exposure_time)
            print(f"✅ 曝光时间已设置为: {exposure_time} us")
        else:
            cam.ExposureAuto.set(gx.GxAutoEntry.CONTINUOUS)
            print("✅ 已设置为: 自动曝光")

        # 2. 设置ROI (使用我们计算好的值)
        if roi_enabled:
            # 注意：必须先设置Offset，再设置Width/Height
            cam.OffsetX.set(offset_x)
            cam.OffsetY.set(offset_y)
            cam.Width.set(width)
            cam.Height.set(height)
        
        # 3. 设置像素格式
        try:
            cam.PixelFormat.set(gx.GxPixelFormatEntry.MONO12)
            print("✅ 像素格式已设置为: Mono12")
        except Exception as e:
            print(f"⚠️ 警告: 无法设置Mono12格式。错误: {e}")
        
        print("--- 配置应用完成 ---\n")

        # --- 获取并打印最终的相机实际配置 ---
        current_width = cam.Width.get()
        current_height = cam.Height.get()
        current_offset_x = cam.OffsetX.get()
        current_offset_y = cam.OffsetY.get()
        pixel_format_str = cam.PixelFormat.get_symbolic()

        print("="*50)
        print("          相机当前实际配置")
        print("="*50)
        print(f"  ROI Offset: X={current_offset_x}, Y={current_offset_y}")
        print(f"  图像尺寸:   {current_width} x {current_height}")
        print(f"  像素格式:   {pixel_format_str}")
        print(f"  曝光模式:   {'自动' if exposure_time == -1 else f'{cam.ExposureTime.get()} us'}")
        print(f"  采集间隔:   {capture_interval} 秒")
        print("="*50 + "\n")

        is_12bit = 'Mono12' in pixel_format_str
        target_dtype = np.uint16 if is_12bit or 'Mono16' in pixel_format_str else np.uint8
        
        # --- 开始采集循环 ---
        cam.stream_on()
        print(">>> 开始采集图像，按 Ctrl+C 停止...")

        while True:
            raw_image = cam.data_stream[0].get_image(timeout=1000)
            if raw_image is None or raw_image.get_status() != 0:
                print("获取图片超时或帧不完整，跳过...")
                continue
            
            numpy_image = raw_image.get_numpy_array()
            if numpy_image is None:
                continue
            
            numpy_image = numpy_image.astype(target_dtype)
            timestamp = time.strftime("%Y%m%d%H%M%S")
            base_filename = f"Pic_{timestamp}_W{current_width}_H{current_height}_F{pixel_format_str}"
            
            # 保存 RAW 文件
            raw_full_path = os.path.join(output_folder, f"{base_filename}.raw")
            numpy_image.tofile(raw_full_path)
            print(f"✅ 已保存 RAW: {base_filename}.raw")

            # 保存 PNG 文件
            try:
                png_image_data = np.left_shift(numpy_image, 4) if is_12bit else numpy_image
                pil_img = Image.fromarray(png_image_data)
                png_full_path = os.path.join(output_folder_png, f"{base_filename}.png")
                pil_img.save(png_full_path, "PNG")
                print(f"✅ 已保存 PNG: {base_filename}.png")
            except Exception as e:
                print(f"❌ 保存 PNG 文件时出错: {e}")

            time.sleep(capture_interval)

    except KeyboardInterrupt:
        print("\n>>> 收到停止指令，正在关闭相机...")
    except Exception as e:
        print(f"❌ 发生未知错误: {str(e)}")

    finally:
        if cam is not None:
            try:
                cam.stream_off()
                print("相机数据流已停止。")
                cam.close_device()
                print("相机已关闭。")
            except Exception as e:
                print(f"关闭相机时出错: {e}")

if __name__ == '__main__':
    capture_images()