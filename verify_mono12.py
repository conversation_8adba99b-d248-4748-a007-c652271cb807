#!/usr/bin/env python3
import gxipy as gx
import numpy as np
import os
import time

def verify_camera_capabilities():
    """Verify camera's supported pixel formats and Mono12 capability"""
    print("="*60)
    print("STEP 1: VERIFYING CAMERA CAPABILITIES")
    print("="*60)
    
    cam = None
    try:
        # Initialize camera
        device_manager = gx.DeviceManager()
        dev_num, dev_info_list = device_manager.update_device_list()
        if dev_num == 0:
            print("❌ No camera devices found")
            return None
            
        cam = device_manager.open_device_by_index(1)
        print("✅ Camera opened successfully")
        
        # Get supported pixel formats
        print("\n📋 Querying supported pixel formats...")
        try:
            # Try to get the pixel format feature
            pixel_format_feature = cam.PixelFormat
            
            # Check if we can enumerate supported formats
            if hasattr(pixel_format_feature, 'get_range'):
                supported_formats = pixel_format_feature.get_range()
                print(f"Supported formats: {supported_formats}")
            elif hasattr(pixel_format_feature, 'get_enum_entry_nums'):
                num_entries = pixel_format_feature.get_enum_entry_nums()
                print(f"Number of supported format entries: {num_entries}")
                
                for i in range(num_entries):
                    try:
                        entry = pixel_format_feature.get_enum_entry_by_index(i)
                        print(f"  Format {i}: {entry}")
                    except:
                        pass
            
            # Check current format
            current_format = pixel_format_feature.get()
            print(f"Current pixel format enum value: {current_format}")
            
            # Try to get symbolic name
            try:
                current_format_name = pixel_format_feature.get_symbolic()
                print(f"Current pixel format name: {current_format_name}")
            except:
                # Use our mapping
                format_map = {17301505: 'Mono8', 17825797: 'Mono12', 17825798: 'Mono16'}
                current_format_name = format_map.get(current_format, f"Unknown_{current_format}")
                print(f"Current pixel format name (mapped): {current_format_name}")
                
        except Exception as e:
            print(f"❌ Error querying pixel formats: {e}")
            
        return cam
        
    except Exception as e:
        print(f"❌ Error initializing camera: {e}")
        return None

def test_format_setting(cam):
    """Test setting different pixel formats and verify actual setting"""
    print("\n" + "="*60)
    print("STEP 2: TESTING PIXEL FORMAT SETTING")
    print("="*60)
    
    formats_to_test = [
        (gx.GxPixelFormatEntry.MONO8, "Mono8", 17301505),
        (gx.GxPixelFormatEntry.MONO12, "Mono12", 17825797),
    ]
    
    results = {}
    
    for format_enum, format_name, expected_value in formats_to_test:
        print(f"\n🔧 Testing {format_name} format...")
        try:
            # Set the format
            cam.PixelFormat.set(format_enum)
            time.sleep(0.1)  # Small delay to ensure setting takes effect
            
            # Verify the setting
            actual_value = cam.PixelFormat.get()
            success = (actual_value == expected_value)
            
            print(f"  Set format: {format_name}")
            print(f"  Expected enum value: {expected_value}")
            print(f"  Actual enum value: {actual_value}")
            print(f"  Setting successful: {'✅' if success else '❌'}")
            
            results[format_name] = {
                'supported': success,
                'enum_value': actual_value,
                'expected_value': expected_value
            }
            
        except Exception as e:
            print(f"  ❌ Failed to set {format_name}: {e}")
            results[format_name] = {'supported': False, 'error': str(e)}
    
    return results

def capture_and_analyze_images(cam, format_results):
    """Capture images in different formats and analyze the data"""
    print("\n" + "="*60)
    print("STEP 3: CAPTURING AND ANALYZING IMAGE DATA")
    print("="*60)
    
    analysis_results = {}
    
    for format_name in ['Mono8', 'Mono12']:
        if not format_results.get(format_name, {}).get('supported', False):
            print(f"\n⏭️  Skipping {format_name} - not supported")
            continue
            
        print(f"\n📸 Capturing {format_name} image...")
        
        try:
            # Set the format
            if format_name == 'Mono8':
                cam.PixelFormat.set(gx.GxPixelFormatEntry.MONO8)
            else:
                cam.PixelFormat.set(gx.GxPixelFormatEntry.MONO12)
            
            time.sleep(0.1)
            
            # Verify format is set
            actual_format = cam.PixelFormat.get()
            print(f"  Confirmed format enum: {actual_format}")
            
            # Start acquisition
            cam.stream_on()
            
            # Capture image
            raw_image = cam.data_stream[0].get_image(timeout=2000)
            if raw_image is None or raw_image.get_status() != 0:
                print(f"  ❌ Failed to capture {format_name} image")
                cam.stream_off()
                continue
                
            # Get numpy array
            numpy_image = raw_image.get_numpy_array()
            cam.stream_off()
            
            # Analyze the image data
            print(f"  📊 Analyzing {format_name} image data:")
            print(f"    Shape: {numpy_image.shape}")
            print(f"    Data type: {numpy_image.dtype}")
            print(f"    Min value: {numpy_image.min()}")
            print(f"    Max value: {numpy_image.max()}")
            print(f"    Mean value: {numpy_image.mean():.2f}")
            print(f"    Unique values count: {len(np.unique(numpy_image))}")
            
            # Check bit depth usage
            max_possible_8bit = 255
            max_possible_12bit = 4095
            
            if numpy_image.max() > max_possible_8bit:
                print(f"    ✅ Values exceed 8-bit range (max: {numpy_image.max()} > 255)")
                actual_bit_depth = "12-bit or higher"
            else:
                print(f"    ⚠️  All values within 8-bit range (max: {numpy_image.max()} ≤ 255)")
                actual_bit_depth = "8-bit range"
            
            # Save for comparison
            filename = f"verify_{format_name.lower()}.raw"
            numpy_image.tofile(filename)
            print(f"    💾 Saved as: {filename}")
            
            analysis_results[format_name] = {
                'shape': numpy_image.shape,
                'dtype': str(numpy_image.dtype),
                'min_val': int(numpy_image.min()),
                'max_val': int(numpy_image.max()),
                'mean_val': float(numpy_image.mean()),
                'unique_count': len(np.unique(numpy_image)),
                'bit_depth_analysis': actual_bit_depth,
                'filename': filename,
                'data_sample': numpy_image.flat[:10].tolist()  # First 10 pixel values
            }
            
        except Exception as e:
            print(f"  ❌ Error capturing {format_name}: {e}")
            analysis_results[format_name] = {'error': str(e)}
    
    return analysis_results

def compare_formats(analysis_results):
    """Compare the captured images to verify format differences"""
    print("\n" + "="*60)
    print("STEP 4: COMPARING FORMAT DIFFERENCES")
    print("="*60)
    
    if 'Mono8' not in analysis_results or 'Mono12' not in analysis_results:
        print("❌ Cannot compare - missing format data")
        return
        
    mono8_data = analysis_results['Mono8']
    mono12_data = analysis_results['Mono12']
    
    if 'error' in mono8_data or 'error' in mono12_data:
        print("❌ Cannot compare - capture errors occurred")
        return
    
    print("📊 Format Comparison:")
    print(f"  Mono8  - Max value: {mono8_data['max_val']}, Dtype: {mono8_data['dtype']}")
    print(f"  Mono12 - Max value: {mono12_data['max_val']}, Dtype: {mono12_data['dtype']}")
    
    print(f"\n  Mono8  - Unique values: {mono8_data['unique_count']}")
    print(f"  Mono12 - Unique values: {mono12_data['unique_count']}")
    
    print(f"\n  Sample pixel values:")
    print(f"  Mono8:  {mono8_data['data_sample']}")
    print(f"  Mono12: {mono12_data['data_sample']}")
    
    # Verify true 12-bit capability
    if mono12_data['max_val'] > 255:
        print(f"\n✅ VERIFICATION PASSED: Mono12 shows values > 255 (max: {mono12_data['max_val']})")
        print("   This confirms true 12-bit data capture capability")
    else:
        print(f"\n⚠️  VERIFICATION INCONCLUSIVE: Mono12 max value ({mono12_data['max_val']}) ≤ 255")
        print("   This could mean: low light conditions, or camera limitation")

def main():
    print("🔍 GALAXY CAMERA MONO12 FORMAT VERIFICATION")
    print("="*60)
    
    # Step 1: Verify camera capabilities
    cam = verify_camera_capabilities()
    if cam is None:
        return
    
    try:
        # Step 2: Test format setting
        format_results = test_format_setting(cam)
        
        # Step 3: Capture and analyze images
        analysis_results = capture_and_analyze_images(cam, format_results)
        
        # Step 4: Compare formats
        compare_formats(analysis_results)
        
        # Final summary
        print("\n" + "="*60)
        print("VERIFICATION SUMMARY")
        print("="*60)
        
        mono12_supported = format_results.get('Mono12', {}).get('supported', False)
        print(f"Mono12 format supported: {'✅' if mono12_supported else '❌'}")
        
        if mono12_supported and 'Mono12' in analysis_results:
            mono12_analysis = analysis_results['Mono12']
            if 'error' not in mono12_analysis:
                max_val = mono12_analysis['max_val']
                dtype = mono12_analysis['dtype']
                print(f"Mono12 max captured value: {max_val}")
                print(f"Mono12 data type: {dtype}")
                
                if max_val > 255 and 'uint16' in dtype:
                    print("🎉 CONCLUSION: True Mono12 format verified!")
                else:
                    print("⚠️  CONCLUSION: Mono12 format questionable - needs investigation")
        
    finally:
        try:
            cam.stream_off()
        except:
            pass
        cam.close_device()
        print("\n🔒 Camera closed")

if __name__ == '__main__':
    main()
