********************************************************

           INSTALL INSTRUCTIONS

               2020-01-13

********************************************************



BEFORE INSTALLATION
====================

Before installing the Galaxy python, Galaxy Linux SDK should be installed first. Otherwise, the python library will not work properly.



================================================

Galaxy python installation for Linux

================================================


Python2.7 gxipy installation  
====================

1.Install python2.7 & python2.7-dev

  (1) sudo apt-get install python2.7

  (2) sudo apt-get install python2.7-dev

2.Install the python-setuptools toolkit
 
  (1) sudo apt-get install python-setuptools
  
3.Install gxipy library

  (1) cd ./api
  
  (2) python setup.py build
  
  (3) sudo python setup.py install

4.Install numpy library

  (1)First method by pip:
  
     1)sudo apt-get install python-pip
  
     2)sudo pip install numpy
     
  (2)Second method by numpy source code:
  
     1)Download source code
     
          wget http://jaist.dl.sourceforge.net/project/numpy/NumPy/1.9.0/numpy-1.9.0.zip
          
     2)unzip numpy-1.9.0.zip
     
     3)cd numpy-1.9.0
     
     4)sudo python setup.py install
     

Python3.5 gxipy installation  
====================

1.Install python3.5 & python3.5-dev

  (1) sudo apt-get install python3.5
  
  (2) sudo apt-get install python3.5-dev

2.Install the python3-setuptools toolkit
 
  (1) sudo apt-get install python3-setuptools
  
3.Install gxipy library

  (1) cd ./api
  
  (2) python3 setup.py build
  
  (3) sudo python3 setup.py install

4.Install numpy library

  (1)First method by pip:
  
     1)sudo apt-get install python3-pip
  
     2)sudo pip3 install numpy
     
  (2)Second method by numpy source code:
  
     1)Download source code
     
          wget http://jaist.dl.sourceforge.net/project/numpy/NumPy/1.9.0/numpy-1.9.0.zip
          
     2)unzip numpy-1.9.0.zip
     
     3)cd numpy-1.9.0
     
     4)sudo python3 setup.py install    


>= Python3.8 installation notice
====================

  Before installing >=python3.8, you should install libffi-dev first.

     sudo apt-get install libffi-dev


Document
====================
doc_en: English version document
doc_cn: Chinese version document


Attention
====================
The sample may depend on third party libraries(e.g. PIL), please install it by yourself.



