#!/usr/bin/env python3
import numpy as np
import os

def analyze_raw_files():
    """Analyze the raw image files to verify bit depth"""
    print("🔍 RAW FILE ANALYSIS")
    print("="*50)
    
    # File information
    mono8_file = "test_mono8.raw"
    mono12_file = "test_mono12.raw"
    
    if not os.path.exists(mono8_file) or not os.path.exists(mono12_file):
        print("❌ Test files not found. Run simple_verify.py first.")
        return
    
    # Get file sizes
    mono8_size = os.path.getsize(mono8_file)
    mono12_size = os.path.getsize(mono12_file)
    
    print(f"📁 File Sizes:")
    print(f"  Mono8:  {mono8_size:,} bytes")
    print(f"  Mono12: {mono12_size:,} bytes")
    print(f"  Ratio:  {mono12_size/mono8_size:.1f}x")
    
    # Expected sizes for 5120x5120 image
    expected_mono8 = 5120 * 5120 * 1  # 1 byte per pixel
    expected_mono12 = 5120 * 5120 * 2  # 2 bytes per pixel (stored as uint16)
    
    print(f"\n📐 Expected vs Actual:")
    print(f"  Mono8  - Expected: {expected_mono8:,}, Actual: {mono8_size:,}, Match: {'✅' if mono8_size == expected_mono8 else '❌'}")
    print(f"  Mono12 - Expected: {expected_mono12:,}, Actual: {mono12_size:,}, Match: {'✅' if mono12_size == expected_mono12 else '❌'}")
    
    # Load and analyze the data
    print(f"\n🔬 Data Analysis:")
    
    # Load Mono8 data
    mono8_data = np.fromfile(mono8_file, dtype=np.uint8).reshape(5120, 5120)
    print(f"  Mono8 loaded: {mono8_data.shape}, dtype: {mono8_data.dtype}")
    print(f"    Min/Max: {mono8_data.min()}/{mono8_data.max()}")
    print(f"    Mean: {mono8_data.mean():.2f}")
    print(f"    Std: {mono8_data.std():.2f}")
    
    # Load Mono12 data
    mono12_data = np.fromfile(mono12_file, dtype=np.uint16).reshape(5120, 5120)
    print(f"  Mono12 loaded: {mono12_data.shape}, dtype: {mono12_data.dtype}")
    print(f"    Min/Max: {mono12_data.min()}/{mono12_data.max()}")
    print(f"    Mean: {mono12_data.mean():.2f}")
    print(f"    Std: {mono12_data.std():.2f}")
    
    # Verify 12-bit range usage
    print(f"\n🎯 Bit Depth Verification:")
    
    # Check if Mono12 uses values beyond 8-bit range
    values_above_255 = np.sum(mono12_data > 255)
    total_pixels = mono12_data.size
    percentage_above_255 = (values_above_255 / total_pixels) * 100
    
    print(f"  Pixels with value > 255: {values_above_255:,} ({percentage_above_255:.2f}%)")
    
    # Check distribution in different bit ranges
    range_8bit = np.sum((mono12_data >= 0) & (mono12_data <= 255))
    range_9to12bit = np.sum((mono12_data > 255) & (mono12_data <= 4095))
    range_above_12bit = np.sum(mono12_data > 4095)
    
    print(f"  Values in 8-bit range (0-255): {range_8bit:,} ({range_8bit/total_pixels*100:.1f}%)")
    print(f"  Values in 9-12bit range (256-4095): {range_9to12bit:,} ({range_9to12bit/total_pixels*100:.1f}%)")
    print(f"  Values above 12-bit (>4095): {range_above_12bit:,} ({range_above_12bit/total_pixels*100:.1f}%)")
    
    # Sample comparison
    print(f"\n🔍 Sample Pixel Comparison (first 10 pixels):")
    print(f"  Mono8:  {mono8_data.flat[:10].tolist()}")
    print(f"  Mono12: {mono12_data.flat[:10].tolist()}")
    
    # Check for bit-shifting patterns (common in fake 12-bit)
    print(f"\n🕵️  Checking for bit-shifting artifacts:")
    
    # If Mono12 is just Mono8 shifted left by 4 bits, values would be multiples of 16
    multiples_of_16 = np.sum(mono12_data % 16 == 0)
    print(f"  Values that are multiples of 16: {multiples_of_16:,} ({multiples_of_16/total_pixels*100:.1f}%)")
    
    if multiples_of_16 / total_pixels > 0.9:  # If >90% are multiples of 16
        print(f"  ⚠️  WARNING: High percentage of multiples of 16 suggests bit-shifting")
    else:
        print(f"  ✅ Good distribution - not just bit-shifted 8-bit data")
    
    # Final verdict
    print(f"\n🏆 FINAL VERIFICATION RESULTS:")
    print(f"="*50)
    print(f"✅ File size ratio correct: {mono12_size/mono8_size:.1f}x (expected 2x)")
    print(f"✅ Mono12 max value: {mono12_data.max()} (proves >8-bit capability)")
    print(f"✅ Uses extended range: {percentage_above_255:.1f}% of pixels > 255")
    
    if mono12_data.max() == 4095:
        print(f"🎉 PERFECT: Maximum value is exactly 4095 (2^12 - 1)")
    elif mono12_data.max() > 255:
        print(f"✅ CONFIRMED: True 12-bit data capture")
    else:
        print(f"⚠️  LIMITED: Current scene doesn't use full 12-bit range")
    
    if multiples_of_16 / total_pixels < 0.5:
        print(f"✅ AUTHENTIC: Not just bit-shifted 8-bit data")
    else:
        print(f"⚠️  SUSPICIOUS: May be bit-shifted 8-bit data")

if __name__ == '__main__':
    analyze_raw_files()
